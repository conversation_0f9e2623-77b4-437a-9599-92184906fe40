from datetime import datetime
from typing import (
    Any,
    Dict,
    Generator,
    Iterable,
    List,
    Literal,
    Optional,
    Sequence,
    Union,
)

import connectorx as cx
import dlt
import pymysql
from dlt.common import logger
from dlt.common.configuration.specs.config_section_context import ConfigSectionContext
from dlt.common.schema.typing import (
    TTableSchemaColumns,
    TWriteDisposition,
)
from dlt.common.typing import TDataItem
from dlt.extract import DltResource
from dlt.extract.items import DataItemWithMeta
from dlt.sources.config import with_config
from dlt.sources.credentials import ConnectionStringCredentials
from pymysqlreplication import BinLogStreamReader
from pymysqlreplication.event import (
    GtidEvent,
    QueryEvent,
    RotateEvent,
    XidEvent,
)
from pymysqlreplication.row_event import (
    DeleteRowsEvent,
    RowsEvent,
    TableMapEvent,
    UpdateRowsEvent,
    WriteRowsEvent,
)

from .constants import CONNECTORX_SUPPORTED_MYSQL_TYPES
from .exceptions import StopReplication


def _build_snapshot_query(
    table_name: str,
    schema_name: str,
    include_columns: Optional[Sequence[str]],
    credentials: ConnectionStringCredentials,
) -> str:
    """Build a SQL query for snapshot with type casting for unsupported MySQL types.

    This function queries the information_schema to get column types and casts
    unsupported types to TEXT to prevent ConnectorX from panicking.

    Args:
        table_name: Name of the table to snapshot
        schema_name: Database schema name
        include_columns: Specific columns to include, if None includes all
        credentials: Database connection credentials

    Returns:
        SQL query string with appropriate type casting
    """
    # Get column information from the database using pymysql
    mysql_settings = _parse_mysql_credentials(credentials)
    connection = pymysql.connect(**mysql_settings)
    try:
        cursor = connection.cursor()
        # Query to get column names and types using parameterized query
        column_query = """
            SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
            ORDER BY ORDINAL_POSITION
        """
        cursor.execute(column_query, (schema_name, table_name))
        columns_info = cursor.fetchall()
    finally:
        connection.close()

    if not columns_info:
        raise ValueError(
            f"Table {schema_name}.{table_name} not found or has no columns"
        )

    # Filter columns if include_columns is specified
    if include_columns:
        include_set = set(include_columns)
        columns_info = [col for col in columns_info if col[0] in include_set]

    # Build SELECT clause with type casting
    select_parts = []
    for column_name, data_type, column_type in columns_info:
        # Use data_type for type checking
        mysql_type = data_type.lower()

        # Check if the type is supported by ConnectorX
        if mysql_type in CONNECTORX_SUPPORTED_MYSQL_TYPES:
            select_parts.append(f"`{column_name}`")
        else:
            # Cast unsupported types to text
            select_parts.append(f"CAST(`{column_name}` AS CHAR) AS `{column_name}`")
            logger.debug(
                f"Casting column {column_name} ({mysql_type}) to text for ConnectorX compatibility"
            )

    # Build the final query
    select_clause = ", ".join(select_parts)
    qualified_table = f"`{schema_name}`.`{table_name}`"

    query = f"SELECT {select_clause} FROM {qualified_table}"

    logger.debug(f"Built snapshot query: {query}")
    return query


class MessageConsumer:
    """Processes MySQL binlog events following PostgreSQL MessageConsumer pattern.

    Maintains event data and processes messages until boundary conditions are met.
    Raises StopReplication when upto_gtid/upto_binlog_pos is reached or target_batch_size is met.
    """

    def __init__(
        self,
        upto_gtid: Optional[str],
        upto_binlog_file: Optional[str],
        upto_binlog_pos: Optional[int],
        target_batch_size: int = 1000,
        include_columns: Optional[Dict[str, Sequence[str]]] = None,
        columns: Optional[Dict[str, TTableSchemaColumns]] = None,
        write_mode: Literal["merge", "append-only"] = "merge",
    ) -> None:
        self.upto_gtid = upto_gtid
        self.upto_binlog_file = upto_binlog_file
        self.upto_binlog_pos = upto_binlog_pos
        self.target_batch_size = target_batch_size
        self.include_columns = include_columns
        self.columns = columns
        self.write_mode = write_mode

        self.consumed_all: bool = False
        # Data items organized by table name - following PostgreSQL pattern
        self.data_items: Dict[str, List[Union[TDataItem, DataItemWithMeta]]] = {}
        # Track table schemas
        self.table_schemas: Dict[str, Dict[str, Any]] = {}
        # Track current position
        self.last_gtid: Optional[str] = None
        self.last_binlog_file: Optional[str] = None
        self.last_binlog_pos: Optional[int] = None
        # Transaction state
        self.in_transaction = False
        self.current_transaction_items = 0

    def __call__(self, event: Any) -> None:
        """Processes event received from binlog stream."""
        self.process_event(event)

    def process_event(self, event: Any) -> None:
        """Processes binlog event and updates internal state.

        Raises StopReplication when:
        - upto_gtid/upto_binlog_pos is reached
        - target_batch_size is reached at transaction boundary
        """
        # Always update position first
        self._update_position(event)

        if isinstance(event, GtidEvent):
            self.process_gtid_event(event)
        elif isinstance(event, XidEvent):
            self.process_commit_event(event)
        elif isinstance(event, TableMapEvent):
            self.process_table_map_event(event)
        elif isinstance(event, RowsEvent):
            self.process_rows_event(event)

    def process_gtid_event(self, event: GtidEvent) -> None:
        """Process GTID event marking transaction start."""
        self.in_transaction = True
        self.current_transaction_items = 0
        self.last_gtid = event.gtid

        # Check if we've reached the GTID boundary
        if self.upto_gtid and _gtid_compare(self.last_gtid, self.upto_gtid) >= 0:
            self.consumed_all = True

    def process_commit_event(self, event: XidEvent) -> None:
        """Process transaction commit event.

        Raises StopReplication when boundary or batch size is reached.
        """
        self.in_transaction = False

        # Check binlog coordinate boundary
        if (
            self.upto_binlog_file
            and self.upto_binlog_pos is not None
            and self.last_binlog_file
            and self.last_binlog_pos is not None
        ):
            if (
                _binlog_position_compare(
                    (self.last_binlog_file, self.last_binlog_pos),
                    (self.upto_binlog_file, self.upto_binlog_pos),
                )
                >= 0
            ):
                self.consumed_all = True

        # Check batch size or boundary reached - only at transaction boundaries
        total_items = sum(len(items) for items in self.data_items.values())
        if self.consumed_all or total_items >= self.target_batch_size:
            raise StopReplication

    def process_table_map_event(self, event: TableMapEvent) -> None:
        """Process table map event to track table schemas."""
        table_name = event.table
        self.table_schemas[table_name] = {
            "name": table_name,
            "schema": event.schema,
            "column_schemas": getattr(event, "column_schemas", []),
        }

        # Initialize data items list for this table if not exists
        if table_name not in self.data_items:
            self.data_items[table_name] = []

    def process_rows_event(self, event: RowsEvent) -> None:
        """Process row change events (INSERT, UPDATE, DELETE)."""
        table_name = event.table

        # Ensure table is initialized
        if table_name not in self.data_items:
            self.data_items[table_name] = []

        for row in event.rows:
            item = self._create_data_item(event, row, table_name)
            if item:
                self.data_items[table_name].append(item)
                self.current_transaction_items += 1

    def _create_data_item(
        self, event: RowsEvent, row: Any, table_name: str
    ) -> Optional[TDataItem]:
        """Create data item from row event."""
        # Determine event type and extract row data
        if isinstance(event, WriteRowsEvent):
            event_type = "INSERT"
            row_data = row["values"] if isinstance(row, dict) else row
        elif isinstance(event, UpdateRowsEvent):
            event_type = "UPDATE"
            row_data = row["after_values"] if isinstance(row, dict) else row
        elif isinstance(event, DeleteRowsEvent):
            event_type = "DELETE"
            row_data = row["values"] if isinstance(row, dict) else row
        else:
            return None

        # Apply column filtering if specified
        include_columns = (
            None
            if self.include_columns is None
            else self.include_columns.get(table_name)
        )

        if include_columns and isinstance(row_data, dict):
            # Filter row data to only include specified columns
            filtered_data = {}
            for col_name, col_value in row_data.items():
                if col_name in include_columns:
                    filtered_data[col_name] = col_value
            row_data = filtered_data

        # Create data item
        if isinstance(row_data, dict):
            item = dict(row_data)
        else:
            # Handle case where row_data is not a dict (fallback)
            item = {"_raw_data": row_data}

        # Add replication metadata
        item["_dlt_event_type"] = event_type
        item["_dlt_event_timestamp"] = datetime.fromtimestamp(event.timestamp)

        if self.last_gtid:
            item["_dlt_gtid"] = self.last_gtid
        if self.last_binlog_file and self.last_binlog_pos:
            item["_dlt_binlog_file"] = self.last_binlog_file
            item["_dlt_binlog_pos"] = self.last_binlog_pos

        # Mark deleted records for merge mode
        if event_type == "DELETE" and self.write_mode == "merge":
            item["_dlt_deleted_at"] = datetime.fromtimestamp(event.timestamp)

        return item

    def _update_position(self, event: Any) -> None:
        """Update current replication position from event."""
        if hasattr(event, "gtid") and event.gtid:
            self.last_gtid = event.gtid

        if hasattr(event, "packet"):
            packet = event.packet
            if hasattr(packet, "log_pos"):
                self.last_binlog_pos = packet.log_pos
            if hasattr(packet, "log_file"):
                self.last_binlog_file = packet.log_file


class ItemGenerator:
    """Generator that yields data items from MySQL replication stream.

    Follows PostgreSQL replication pattern for transaction-aware batch processing.
    """

    def __init__(
        self,
        server_id: int,
        schema_name: str,
        table_names: List[str],
        credentials: ConnectionStringCredentials,
        include_columns: Optional[Dict[str, Sequence[str]]],
        columns: Optional[Dict[str, TTableSchemaColumns]],
        target_batch_size: int,
        write_mode: Literal["merge", "append-only"],
        start_gtid: Optional[str] = None,
        upto_gtid: Optional[str] = None,
        start_binlog_file: Optional[str] = None,
        start_binlog_pos: Optional[int] = None,
        upto_binlog_file: Optional[str] = None,
        upto_binlog_pos: Optional[int] = None,
        only_events: Optional[List[str]] = None,
        ignored_events: Optional[List[str]] = None,
    ):
        self.server_id = server_id
        self.schema_name = schema_name
        self.table_names = table_names
        self.credentials = credentials
        self.target_batch_size = target_batch_size

        # Create message consumer
        self.consumer = MessageConsumer(
            upto_gtid=upto_gtid,
            upto_binlog_file=upto_binlog_file,
            upto_binlog_pos=upto_binlog_pos,
            target_batch_size=target_batch_size,
            include_columns=include_columns,
            columns=columns,
            write_mode=write_mode,
        )

        # Stream configuration
        self.start_gtid = start_gtid
        self.start_binlog_file = start_binlog_file
        self.start_binlog_pos = start_binlog_pos
        self.only_events = only_events or [
            WriteRowsEvent,
            UpdateRowsEvent,
            DeleteRowsEvent,
            GtidEvent,
            XidEvent,
            TableMapEvent,
        ]
        self.ignored_events = ignored_events or []

        self.generated_all = False

    def __iter__(self) -> Generator[List[TDataItem], None, None]:
        """Yields batches of data items from MySQL replication stream."""
        mysql_settings = _parse_mysql_credentials(self.credentials)

        # Create binlog stream reader
        stream = BinLogStreamReader(
            connection_settings=mysql_settings,
            server_id=self.server_id,
            only_events=self.only_events,
            ignored_events=self.ignored_events,
            auto_position=self.start_gtid,
            log_file=self.start_binlog_file,
            log_pos=self.start_binlog_pos,
            blocking=False,  # Non-blocking to allow clean exit
            only_schemas=[self.schema_name],
            only_tables=self.table_names,
        )

        try:
            # Process events until StopReplication is raised
            for event in stream:
                try:
                    self.consumer.process_event(event)
                except StopReplication:
                    # Yield final batch and mark as complete
                    final_batch = self._create_batch()
                    if final_batch:
                        yield final_batch
                    self.generated_all = True
                    return

            # If stream ends naturally (no more events), mark as complete
            self.generated_all = True
            final_batch = self._create_batch()
            if final_batch:
                yield final_batch

        finally:
            stream.close()

    def _create_batch(self) -> List[TDataItem]:
        """Create batch from accumulated data items."""
        batch = []
        for table_name, items in self.consumer.data_items.items():
            if items:
                # Add table metadata for first item
                if items and table_name in self.consumer.table_schemas:
                    # Create table hints based on write mode
                    write_disposition: TWriteDisposition = "append"
                    if self.consumer.write_mode == "merge":
                        write_disposition = "merge"

                    # Add table schema hints
                    table_hints = dlt.mark.with_hints(
                        items,
                        dlt.mark.make_hints(
                            table_name=table_name,
                            write_disposition=write_disposition,
                        ),
                    )
                    batch.extend(table_hints)
                else:
                    batch.extend(items)

        # Clear processed items
        self.consumer.data_items.clear()
        return batch

    @property
    def last_gtid(self) -> Optional[str]:
        """Get last processed GTID."""
        return self.consumer.last_gtid

    @property
    def last_binlog_file(self) -> Optional[str]:
        """Get last processed binlog file."""
        return self.consumer.last_binlog_file

    @property
    def last_binlog_pos(self) -> Optional[int]:
        """Get last processed binlog position."""
        return self.consumer.last_binlog_pos

    @property
    def consumed_all(self) -> bool:
        """Check if all events have been consumed."""
        return self.consumer.consumed_all


def init_replication(
    server_id: int,
    schema_name: str,
    table_names: Optional[Union[str, List[str]]] = None,
    credentials: ConnectionStringCredentials = dlt.secrets.value,
    include_columns: Optional[Dict[str, Sequence[str]]] = None,
    columns: Optional[Dict[str, TTableSchemaColumns]] = None,
    reset: bool = False,
    include_tables: Optional[List[str]] = None,
    exclude_tables: Optional[List[str]] = None,
    initial_snapshots: bool = True,
) -> Generator[List[TDataItem], None, None]:
    """Initialize replication by taking initial snapshots of tables.

    This function creates initial snapshots of the specified tables before
    starting binlog replication. It's equivalent to the PostgreSQL init_replication.

    Args:
        server_id: MySQL server ID for replication
        schema_name: Database schema name
        table_names: Optional list of table names to replicate
        credentials: MySQL connection credentials
        include_columns: Optional dict mapping table names to column lists
        columns: Optional dict mapping table names to column schemas

    Yields:
        List[TDataItem]: Initial snapshot data for each table
    """
    # Get MySQL connection
    mysql_settings = _parse_mysql_credentials(credentials)

    # Discover tables if not specified
    if table_names is None:
        discovered_tables = discover_schema_tables(
            schema_name=schema_name,
            credentials=credentials,
            include_tables=None,
            exclude_tables=None,
        )
    else:
        table_names_list = (
            [table_names] if isinstance(table_names, str) else table_names
        )
        discovered_tables = filter_tables(
            discover_schema_tables(schema_name, credentials), table_names_list, None
        )

    # Take snapshot of each table using ConnectorX
    for table_name in discovered_tables:
        # Build the SQL query with type casting for unsupported types
        query = _build_snapshot_query(
            table_name,
            schema_name,
            include_columns.get(table_name) if include_columns else None,
            credentials,
        )

        # Use ConnectorX to read data
        try:
            data = cx.read_sql(
                conn=credentials.to_native_representation(),
                query=query,
                protocol="binary",
                return_type="arrow",
            )

            # Yield the data if any was returned
            if len(data) > 0:
                yield data
            else:
                logger.info(f"No data found for {schema_name}.{table_name}")

        except Exception as e:
            logger.error(f"Error reading snapshot for {schema_name}.{table_name}: {e}")
            raise


def replication_resource(
    server_id: int,
    schema_name: str,
    table_names: Optional[Union[str, List[str]]] = None,
    credentials: ConnectionStringCredentials = dlt.secrets.value,
    target_batch_size: int = 1000,
    write_mode: Literal["merge", "append-only"] = "merge",
    only_events: Optional[List[str]] = None,
    ignored_events: Optional[List[str]] = None,
    include_columns: Optional[Dict[str, Sequence[str]]] = None,
    columns: Optional[Dict[str, TTableSchemaColumns]] = None,
    include_tables: Optional[List[str]] = None,
    exclude_tables: Optional[List[str]] = None,
) -> DltResource:
    """MySQL replication resource for ongoing binlog replication.

    This function handles ongoing binlog replication after initial snapshots.
    It's equivalent to the PostgreSQL replication_resource.

    Args:
        server_id: MySQL server ID for replication
        schema_name: Database schema name
        table_names: Optional list of table names to replicate
        credentials: MySQL connection credentials
        target_batch_size: Target number of items per batch
        write_mode: Write mode (merge or append-only)
        only_events: Optional list of events to include
        ignored_events: Optional list of events to ignore
        include_columns: Optional dict mapping table names to column lists
        columns: Optional dict mapping table names to column schemas

    Returns:
        DltResource: MySQL replication resource
    """
    return mysql_replication_resource(
        server_id=server_id,
        schema_name=schema_name,
        table_names=table_names,
        credentials=credentials,
        target_batch_size=target_batch_size,
        write_mode=write_mode,
        only_events=only_events,
        ignored_events=ignored_events,
        include_columns=include_columns,
        columns=columns,
    )


@with_config(sections=("sources", "mysql_replication"), spec=ConfigSectionContext)
def mysql_replication_resource(
    credentials: ConnectionStringCredentials = dlt.secrets.value,
    server_id: int = dlt.config.value,
    schema_name: str = dlt.config.value,
    table_names: Optional[Union[str, List[str]]] = None,
    include_columns: Optional[Dict[str, Sequence[str]]] = None,
    columns: Optional[Dict[str, TTableSchemaColumns]] = None,
    target_batch_size: int = 1000,
    write_mode: Literal["merge", "append-only"] = "merge",
    only_events: Optional[List[str]] = None,
    ignored_events: Optional[List[str]] = None,
) -> DltResource:
    """MySQL replication resource following PostgreSQL pattern.

    Args:
        credentials: MySQL connection credentials
        server_id: Unique server ID for replication
        schema_name: Database schema name
        table_names: Tables to replicate (None for all)
        include_columns: Columns to include per table
        columns: Column schema hints per table
        target_batch_size: Target batch size for processing
        write_mode: Write mode (merge or append-only)
        only_events: Event types to process
        ignored_events: Event types to ignore

    Returns:
        DltResource for MySQL replication
    """

    def _mysql_replication() -> Generator[List[TDataItem], None, None]:
        """Generator function for MySQL replication data."""
        # Get resource state
        resource_state = dlt.current.resource_state()

        # Discover tables if not specified
        if table_names is None:
            discovered_tables = discover_schema_tables(schema_name, credentials)
        else:
            table_names_list = (
                [table_names] if isinstance(table_names, str) else table_names
            )
            discovered_tables = filter_tables(
                discover_schema_tables(schema_name, credentials), table_names_list, None
            )

        if not discovered_tables:
            logger.warning(f"No tables found in schema '{schema_name}'")
            return

        logger.info(f"Replicating tables: {discovered_tables}")

        # Determine replication mode and starting position
        use_gtid = is_gtid_enabled(credentials)

        if use_gtid:
            start_gtid = resource_state.get("last_gtid")
            if not start_gtid:
                # Get current GTID position for initial sync
                start_gtid = get_current_gtid(credentials)
                logger.info(f"Starting GTID replication from: {start_gtid}")

            # For production: set reasonable boundaries
            # In this implementation, we process all available events
            upto_gtid = None  # Process all available events
            start_binlog_file = None
            start_binlog_pos = None
            upto_binlog_file = None
            upto_binlog_pos = None
        else:
            start_binlog_file = resource_state.get("last_binlog_file")
            start_binlog_pos = resource_state.get("last_binlog_pos")

            if not start_binlog_file or start_binlog_pos is None:
                # Get current binlog position for initial sync
                start_binlog_file, start_binlog_pos = get_current_binlog_position(
                    credentials
                )
                logger.info(
                    f"Starting binlog replication from: {start_binlog_file}:{start_binlog_pos}"
                )

            start_gtid = None
            upto_gtid = None
            upto_binlog_file = None
            upto_binlog_pos = None

        # Create item generator
        item_generator = ItemGenerator(
            server_id=server_id,
            schema_name=schema_name,
            table_names=discovered_tables,
            credentials=credentials,
            include_columns=include_columns,
            columns=columns,
            target_batch_size=target_batch_size,
            write_mode=write_mode,
            start_gtid=start_gtid,
            upto_gtid=upto_gtid,
            start_binlog_file=start_binlog_file,
            start_binlog_pos=start_binlog_pos,
            upto_binlog_file=upto_binlog_file,
            upto_binlog_pos=upto_binlog_pos,
            only_events=only_events,
            ignored_events=ignored_events,
        )

        try:
            # Yield batches until all events are consumed
            yield from item_generator

            # Update state with final position
            if use_gtid and item_generator.last_gtid:
                resource_state["last_gtid"] = item_generator.last_gtid
                # Clear binlog state when using GTID
                resource_state.pop("last_binlog_file", None)
                resource_state.pop("last_binlog_pos", None)
            elif (
                not use_gtid
                and item_generator.last_binlog_file
                and item_generator.last_binlog_pos is not None
            ):
                resource_state["last_binlog_file"] = item_generator.last_binlog_file
                resource_state["last_binlog_pos"] = item_generator.last_binlog_pos
                # Clear GTID state when using binlog coordinates
                resource_state.pop("last_gtid", None)

        except Exception as e:
            logger.error(f"Error in MySQL replication: {e}")
            # Save current position before re-raising
            if use_gtid and item_generator.last_gtid:
                resource_state["last_gtid"] = item_generator.last_gtid
            elif (
                not use_gtid
                and item_generator.last_binlog_file
                and item_generator.last_binlog_pos is not None
            ):
                resource_state["last_binlog_file"] = item_generator.last_binlog_file
                resource_state["last_binlog_pos"] = item_generator.last_binlog_pos
            raise

    return dlt.resource(
        _mysql_replication,
        name="mysql_replication",
        write_disposition="merge" if write_mode == "merge" else "append",
        columns=columns,
    )


def discover_schema_tables(
    schema_name: str,
    credentials: ConnectionStringCredentials,
    include_tables: Optional[Sequence[str]] = None,
    exclude_tables: Optional[Sequence[str]] = None,
) -> List[str]:
    """Discover tables in the specified schema.

    Args:
        schema_name: Database schema name
        credentials: MySQL connection credentials
        include_tables: Optional list of tables to include
        exclude_tables: Optional list of tables to exclude

    Returns:
        List of table names
    """
    try:
        # Use pymysql to query information schema with parameterized query
        mysql_settings = _parse_mysql_credentials(credentials)
        connection = pymysql.connect(**mysql_settings)
        try:
            cursor = connection.cursor()
            query = """
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = %s AND TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
            """
            cursor.execute(query, (schema_name,))
            all_tables = [row[0] for row in cursor.fetchall()]
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"Failed to discover tables in schema {schema_name}: {e}")
        raise

    # Apply table filtering
    return filter_tables(all_tables, include_tables, exclude_tables)


def filter_tables(
    all_tables: List[str],
    include_tables: Optional[Sequence[str]] = None,
    exclude_tables: Optional[Sequence[str]] = None,
) -> List[str]:
    """Filter table list based on include/exclude patterns.

    Args:
        all_tables: List of all available tables
        include_tables: Optional list of tables to include
        exclude_tables: Optional list of tables to exclude

    Returns:
        Filtered list of table names
    """
    filtered_tables = list(all_tables)

    # Apply include filter
    if include_tables:
        include_set = set(include_tables)
        filtered_tables = [t for t in filtered_tables if t in include_set]

    # Apply exclude filter
    if exclude_tables:
        exclude_set = set(exclude_tables)
        filtered_tables = [t for t in filtered_tables if t not in exclude_set]

    return filtered_tables


def is_gtid_enabled(credentials: ConnectionStringCredentials) -> bool:
    """Check if GTID is enabled on MySQL server."""
    try:
        # Use pymysql to check GTID status
        mysql_settings = _parse_mysql_credentials(credentials)
        connection = pymysql.connect(**mysql_settings)
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT @@gtid_mode AS gtid_mode")
            result = cursor.fetchone()
            if result:
                gtid_mode = result[0]
                return gtid_mode in ("ON", "ON_PERMISSIVE")
            else:
                return False
        finally:
            connection.close()

    except Exception as e:
        logger.warning(f"Could not check GTID status: {e}")
        return False


def get_current_gtid(credentials: ConnectionStringCredentials) -> str:
    """Get current GTID position from MySQL server."""
    try:
        # Use pymysql to get current GTID
        mysql_settings = _parse_mysql_credentials(credentials)
        connection = pymysql.connect(**mysql_settings)
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT @@gtid_executed AS gtid_executed")
            result = cursor.fetchone()
            if result:
                gtid_executed = result[0]
                return gtid_executed if gtid_executed is not None else ""
            else:
                return ""
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"Failed to get current GTID: {e}")
        raise


def get_current_binlog_position(
    credentials: ConnectionStringCredentials,
) -> tuple[str, int]:
    """Get current binlog position from MySQL server."""
    try:
        # Use pymysql to get master status
        mysql_settings = _parse_mysql_credentials(credentials)
        connection = pymysql.connect(**mysql_settings)
        try:
            cursor = connection.cursor()
            cursor.execute("SHOW MASTER STATUS")
            result = cursor.fetchone()
            if result:
                # SHOW MASTER STATUS returns File, Position, Binlog_Do_DB, Binlog_Ignore_DB, Executed_Gtid_Set
                file = result[0]  # First column is File
                position = result[1]  # Second column is Position
                return file, int(position)
            else:
                raise ValueError("Could not get master status")
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"Failed to get current binlog position: {e}")
        raise


def _parse_mysql_credentials(
    credentials: ConnectionStringCredentials,
) -> Dict[str, Any]:
    """Parse MySQL credentials for pymysql connection."""
    # Parse connection string
    import urllib.parse

    parsed = urllib.parse.urlparse(credentials.to_native_representation())

    return {
        "host": parsed.hostname,
        "port": parsed.port or 3306,
        "user": parsed.username,
        "password": parsed.password,
        "database": parsed.path.lstrip("/") if parsed.path else None,
        "charset": "utf8mb4",
        "autocommit": True,
    }


def _gtid_compare(gtid1: str, gtid2: str) -> int:
    """Compare two GTID strings.

    Returns:
        -1 if gtid1 < gtid2
         0 if gtid1 == gtid2
         1 if gtid1 > gtid2
    """
    # Simple string comparison for now
    # In production, implement proper GTID comparison logic
    if gtid1 == gtid2:
        return 0
    elif gtid1 < gtid2:
        return -1
    else:
        return 1


def _binlog_position_compare(pos1: tuple[str, int], pos2: tuple[str, int]) -> int:
    """Compare two binlog positions.

    Returns:
        -1 if pos1 < pos2
         0 if pos1 == pos2
         1 if pos1 > pos2
    """
    file1, offset1 = pos1
    file2, offset2 = pos2

    # Extract numeric part from binlog filename for comparison
    def extract_binlog_number(filename: str) -> int:
        import re

        match = re.search(r"\.(\d+)$", filename)
        return int(match.group(1)) if match else 0

    num1 = extract_binlog_number(file1)
    num2 = extract_binlog_number(file2)

    if num1 < num2:
        return -1
    elif num1 > num2:
        return 1
    else:
        # Same file, compare positions
        if offset1 < offset2:
            return -1
        elif offset1 > offset2:
            return 1
        else:
            return 0


def check_mysql_replication_setup(
    credentials: ConnectionStringCredentials,
) -> Dict[str, Any]:
    """Check MySQL server configuration for replication.

    Returns:
        Dictionary with configuration status
    """
    status = {}

    try:
        # Use pymysql to check MySQL replication configuration
        mysql_settings = _parse_mysql_credentials(credentials)
        connection = pymysql.connect(**mysql_settings)
        try:
            cursor = connection.cursor()
            cursor.execute("""SELECT 
                @@binlog_format AS binlog_format,
                @@log_bin AS log_bin,
                @@gtid_mode AS gtid_mode,
                @@server_id AS server_id
            """)
            result = cursor.fetchone()
            if result:
                status["binlog_format"] = result[0]
                log_bin_value = result[1]
                status["log_bin"] = (
                    bool(log_bin_value) if log_bin_value is not None else False
                )
                status["gtid_mode"] = result[2]
                status["server_id"] = result[3]
            else:
                status["error"] = "No configuration data returned"
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"Failed to check MySQL replication setup: {e}")
        status["error"] = str(e)

    return status
