# MySQL Replication

[MySQL](https://www.mysql.com/) is one of the most popular relational database management systems. This source uses MySQL's binary log replication functionality to efficiently process changes in tables (a process often referred to as _Change Data Capture_ or CDC). It uses MySQL's binary log with GTID (Global Transaction Identifier) for reliable change tracking.

## Features

- **Real-time Change Data Capture**: Uses MySQL binary log replication to capture INSERT, UPDATE, and DELETE operations
- **GTID-based Positioning**: Reliable replication positioning using MySQL Global Transaction Identifiers
- **Initial Snapshots**: Optional initial table snapshots using ConnectorX for high performance
- **Table Filtering**: Include/exclude tables using glob patterns
- **Column Filtering**: Include specific columns per table
- **State Management**: Automatic tracking of replication position for resumable operations
- **Batch Processing**: Configurable batch sizes for optimal performance
- **Write Modes**: Support for both merge and append-only write modes

## Requirements

### MySQL Server Configuration

The MySQL server must be configured for binary log replication:

1. **Enable Binary Logging**:
   ```sql
   -- Add to my.cnf or my.ini
   [mysqld]
   log-bin=mysql-bin
   server-id=1
   binlog-format=ROW
   gtid-mode=ON
   enforce-gtid-consistency=ON
   ```

2. **Create Replication User**:
   ```sql
   CREATE USER 'replication_user'@'%' IDENTIFIED BY 'password';
   GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'replication_user'@'%';
   GRANT SELECT ON your_database.* TO 'replication_user'@'%';
   FLUSH PRIVILEGES;
   ```

3. **Restart MySQL** to apply configuration changes.

### Python Dependencies

Install required packages:
```bash
pip install dlt[mysql] pymysql python-mysql-replication connectorx
```

## Usage

### Basic Example

```python
import dlt
from dlt_providers.sources.mysql_replication import mysql_replication

# Configure the source
source = mysql_replication(
    server_id=100,  # Unique server ID for this replication connection
    schema_name="your_database",
    credentials="mysql://replication_user:password@localhost:3306/your_database"
)

# Create pipeline
pipeline = dlt.pipeline(
    pipeline_name="mysql_replication_pipeline",
    destination="duckdb",
    dataset_name="replicated_data"
)

# Run initial load with snapshots
info = pipeline.run(source)
print(info)

# Run incremental updates
info = pipeline.run(source)
print(info)
```

### Advanced Configuration

```python
import dlt
from dlt_providers.sources.mysql_replication import mysql_replication

# Advanced configuration
source = mysql_replication(
    server_id=100,
    schema_name="your_database",
    credentials="mysql://replication_user:password@localhost:3306/your_database",
    
    # Table filtering
    include_tables=["users", "orders", "products*"],  # Glob patterns
    exclude_tables=["*_temp", "*_backup"],
    
    # Column filtering
    include_columns={
        "users": ["id", "name", "email", "created_at"],
        "orders": ["id", "user_id", "total", "status"]
    },
    
    # Schema hints
    columns={
        "users": {
            "id": {"data_type": "bigint", "primary_key": True},
            "created_at": {"data_type": "timestamp"}
        }
    },
    
    # Replication settings
    target_batch_size=5000,
    write_mode="merge",  # or "append-only"
    start_gtid=None,  # Start from current position
    
    # Event filtering
    only_events=None,  # Include all events by default
    ignored_events=None,
    
    # Performance settings
    initial_snapshots=True
)

pipeline = dlt.pipeline(
    pipeline_name="mysql_replication_advanced",
    destination="snowflake",
    dataset_name="replicated_data"
)

# Run pipeline
info = pipeline.run(source)
print(info)
```

### Configuration Options

#### Core Parameters

- **server_id** (int): Unique server ID for MySQL replication connection (required)
- **schema_name** (str): MySQL database/schema name (required)
- **credentials** (ConnectionStringCredentials): MySQL connection credentials (required)

#### Table and Column Filtering

- **table_names** (Optional[Union[str, Sequence[str]]]): Specific table names to replicate
- **include_tables** (Optional[Union[str, List[str]]]): Glob patterns for tables to include
- **exclude_tables** (Optional[Union[str, List[str]]]): Glob patterns for tables to exclude
- **include_columns** (Optional[Dict[str, Sequence[str]]]): Column names to include per table
- **columns** (Optional[Dict[str, TTableSchemaColumns]]): Schema hints per table

#### Replication Settings

- **target_batch_size** (int, default=1000): Number of changes to process in each batch
- **write_mode** (Literal["merge", "append-only"], default="merge"): How to handle updates
- **start_gtid** (Optional[str]): GTID position to start replication from
- **only_events** (Optional[List[str]]): Event types to include (INSERT, UPDATE, DELETE)
- **ignored_events** (Optional[List[str]]): Event types to ignore
- **initial_snapshots** (bool, default=True): Create initial table snapshots
- **reset** (bool, default=False): Reset replication state and start from beginning

## Resources

The source provides these resources:

1. **Snapshot Resources**: Initial table snapshots (when `initial_snapshots=True`)
   - Named as `{schema_name}_{table_name}`
   - Use ConnectorX for high-performance data loading
   - Automatically track completion state

2. **Replication Resource**: Ongoing change data capture
   - Named as `mysql_replication_{server_id}`
   - Processes binary log events in real-time
   - Maintains GTID position in state

## State Management

The source automatically manages replication state:

- **GTID Position**: Tracks the last processed Global Transaction Identifier
- **Snapshot Completion**: Tracks which table snapshots have been completed
- **Resumable**: Can resume replication from the last known position

## Data Types

Supported MySQL data types:

| MySQL Type | DLT Type | Notes |
|------------|----------|-------|
| TINYINT, SMALLINT, MEDIUMINT, INT, BIGINT | bigint | |
| BIT | bool | |
| FLOAT, DOUBLE, REAL | double | |
| DECIMAL, NUMERIC | decimal | Preserves precision and scale |
| CHAR, VARCHAR, TEXT variants | text | |
| BINARY, VARBINARY, BLOB variants | binary | |
| DATE | date | |
| TIME | time | |
| DATETIME, TIMESTAMP | timestamp | Converted to Pendulum datetime |
| YEAR | bigint | |
| JSON | complex | Parsed as JSON object |
| ENUM, SET | text | |
| Spatial types | text | Converted to hex string |

## Metadata Columns

The replication resource adds these metadata columns:

- **_dlt_gtid**: MySQL Global Transaction Identifier for the change
- **_dlt_event_type**: Type of change (INSERT, UPDATE, DELETE)
- **_dlt_timestamp**: Timestamp when the change was processed
- **_dlt_deleted_ts**: Timestamp for deleted records (DELETE events only)

## Write Modes

### Merge Mode (default)
- Uses primary keys to merge changes
- Updates existing records, inserts new ones
- Handles DELETE events by setting `_dlt_deleted_ts`
- Requires primary key on target tables

### Append-Only Mode
- Appends all changes as new records
- Preserves full change history
- No deduplication
- Suitable for audit trails

## Troubleshooting

### Common Issues

1. **"MySQL server has gone away"**
   - Check MySQL `wait_timeout` and `interactive_timeout` settings
   - Ensure stable network connection

2. **"Replication user lacks privileges"**
   - Verify REPLICATION SLAVE and REPLICATION CLIENT grants
   - Ensure SELECT privileges on target schema

3. **"GTID mode not enabled"**
   - Enable GTID mode in MySQL configuration
   - Restart MySQL server

4. **"Binary logging not enabled"**
   - Enable binary logging in MySQL configuration
   - Set `binlog-format=ROW`
   - Restart MySQL server

### Performance Tuning

1. **Batch Size**: Adjust `target_batch_size` based on your workload
2. **Column Filtering**: Use `include_columns` to reduce data volume
3. **Table Filtering**: Use `include_tables`/`exclude_tables` to limit scope
4. **ConnectorX**: Ensure ConnectorX is installed for fast snapshots

## Examples

### E-commerce Database Replication

```python
import dlt
from dlt_providers.sources.mysql_replication import mysql_replication

# Replicate e-commerce tables
source = mysql_replication(
    server_id=200,
    schema_name="ecommerce",
    credentials="mysql://repl_user:<EMAIL>:3306/ecommerce",
    include_tables=["users", "orders", "order_items", "products"],
    write_mode="merge",
    target_batch_size=2000
)

pipeline = dlt.pipeline(
    pipeline_name="ecommerce_replication",
    destination="bigquery",
    dataset_name="ecommerce_replica"
)

info = pipeline.run(source)
print(f"Loaded {info.loads_ids} loads")
```

### Audit Trail with Append-Only Mode

```python
import dlt
from dlt_providers.sources.mysql_replication import mysql_replication

# Create audit trail of all changes
source = mysql_replication(
    server_id=300,
    schema_name="production",
    credentials="mysql://audit_user:password@prod-mysql:3306/production",
    write_mode="append-only",  # Keep all change history
    initial_snapshots=False,   # Skip initial snapshots for audit
    target_batch_size=10000
)

pipeline = dlt.pipeline(
    pipeline_name="audit_trail",
    destination="postgres",
    dataset_name="audit_log"
)

info = pipeline.run(source)
print(f"Audit trail updated: {info}")
```

### Selective Table Replication

```python
import dlt
from dlt_providers.sources.mysql_replication import mysql_replication

# Replicate only specific tables with column filtering
source = mysql_replication(
    server_id=400,
    schema_name="analytics",
    credentials="mysql://analytics_user:password@analytics-mysql:3306/analytics",
    table_names=["events", "users", "sessions"],
    include_columns={
        "events": ["id", "user_id", "event_type", "timestamp", "properties"],
        "users": ["id", "email", "created_at", "last_active"],
        "sessions": ["id", "user_id", "started_at", "ended_at"]
    },
    write_mode="append-only",
    target_batch_size=1000
)

pipeline = dlt.pipeline(
    pipeline_name="analytics_replication",
    destination="clickhouse",
    dataset_name="analytics_events"
)

info = pipeline.run(source)
print(f"Analytics data replicated: {info}")
```

## Binlog Coordinate Support

The MySQL replication source supports both **GTID (Global Transaction Identifier)** and **binlog coordinate** modes for replication. The system automatically detects which mode to use based on MySQL server configuration and user preferences.

### Automatic Mode Detection
- **GTID Mode**: Used when GTID is enabled on the MySQL server and `start_gtid` is provided
- **Binlog Coordinate Mode**: Used when GTID is not available or when `start_binlog_file`/`start_binlog_pos` are provided
- **Fallback**: Automatically falls back to binlog coordinates if GTID is not properly configured

### Automatic State Management

The MySQL replication source automatically manages replication state:

- **First Run**: Starts from the current binlog position
- **Subsequent Runs**: Resumes from the last processed position stored in dlt state
- **Mode Selection**: Automatically chooses GTID mode if available, otherwise uses binlog coordinates
- **State Persistence**: Position is saved after each successful batch

### Usage Examples

#### Basic Usage (Automatic Mode)
```python
import dlt
from dlt_providers.sources.mysql_replication import mysql_replication

# Automatic mode selection and state management
source = mysql_replication(
    server_id=1,
    schema_name="my_database",
    credentials="mysql://replication_user:password@localhost:3306/my_database"
)

pipeline = dlt.pipeline(
    pipeline_name="mysql_replication",
    destination="duckdb",
    dataset_name="mysql_data"
)

# First run: starts from current position
info = pipeline.run(source)
print(info)

# Subsequent runs: automatically resume from last position
info = pipeline.run(source)
print(info)
```

#### Resetting Replication State
```python
# To start replication from the beginning
source = mysql_replication(
    server_id=1,
    schema_name="my_database",
    credentials=credentials,
    reset=True  # Clears existing state and starts fresh
)
```

### Implementation Details

#### New Helper Functions

- **`is_gtid_enabled(credentials)`**: Checks if GTID is enabled and available for replication
- **`get_current_binlog_position(credentials)`**: Retrieves the current binlog file and position
- **`_binlog_position_compare(file1, pos1, file2, pos2)`**: Compares two binlog positions

#### Enhanced ItemGenerator Class

The `ItemGenerator` class now supports both replication modes with:
- Automatic mode detection based on GTID availability and parameters
- Position tracking for both GTID and binlog coordinates
- Boundary checking for both modes

#### State Management

The replication resource maintains state differently based on the mode:

**GTID Mode State:**
```python
{
    "last_gtid": "3E11FA47-71CA-11E1-9E33-C80AA9429562:1-10"
}
```

**Binlog Coordinate Mode State:**
```python
{
    "last_binlog_file": "mysql-bin.000001",
    "last_binlog_pos": 2000
}
```

#### Metadata Fields

Data items include different metadata based on the replication mode:

**GTID Mode:**
- `_dlt_gtid`: The GTID of the transaction containing this change

**Binlog Coordinate Mode:**
- `_dlt_binlog_file`: The binlog file containing this change
- `_dlt_binlog_pos`: The binlog position of this change

### Error Handling and Fallback

- If GTID is requested but not available, the system logs an info message and falls back to binlog coordinates
- If neither mode is explicitly specified, the system chooses the best available option
- Validates binlog coordinate parameters when provided
- Ensures consistent state management between pipeline runs

### Performance Considerations

**GTID Mode (Recommended):**
- **Pros**: More robust, handles server failover, easier to manage
- **Cons**: Requires GTID to be enabled on MySQL server

**Binlog Coordinate Mode:**
- **Pros**: Works with any MySQL configuration, precise position control
- **Cons**: More fragile, requires careful position tracking, doesn't handle failover

### Migration Guide

#### From GTID-only Implementation
Existing pipelines using GTID will continue to work without changes. The new parameters are optional and backward compatible.

#### Enabling Binlog Coordinate Support
To use binlog coordinates for an existing pipeline:

1. **Determine current position**:
   ```sql
   SHOW MASTER STATUS;
   ```

2. **Update pipeline configuration**:
   ```python
   source = mysql_replication(
       # ... existing parameters ...
       start_binlog_file="mysql-bin.000001",
       start_binlog_pos=1000
   )
   ```

3. **Reset pipeline state** (if switching modes):
   ```python
   source = mysql_replication(
       # ... parameters ...
       reset=True  # Clears existing state
   )
   ```

### Troubleshooting Binlog Coordinates

1. **GTID not available**:
   - Check MySQL configuration: `SELECT @@gtid_mode, @@enforce_gtid_consistency;`
   - Enable GTID if needed or use binlog coordinates

2. **Invalid binlog position**:
   - Verify binlog file exists: `SHOW BINARY LOGS;`
   - Check position is valid: `SHOW BINLOG EVENTS IN 'mysql-bin.000001' LIMIT 10;`

3. **Position tracking issues**:
   - Reset pipeline state with `reset=True`
   - Verify MySQL user has REPLICATION SLAVE privileges

The binlog coordinate support provides a robust fallback mechanism for MySQL replication when GTID is not available or desired, maintaining backward compatibility while adding flexibility for different MySQL configurations and use cases.
```