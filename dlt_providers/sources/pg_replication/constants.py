# PostgreSQL types that are supported by ConnectorX
# https://sfu-db.github.io/connector-x/databases/postgres.html
# https://github.com/sfu-db/connector-x/blob/main/connectorx/src/transports/postgres_arrow.rs
# Types that are NOT in this set should be cast to TEXT
CONNECTORX_SUPPORTED_PG_TYPES = {
    "bool",
    "boolean",
    "int2",
    "smallint",
    "int4",
    "int",
    "integer",
    "int8",
    "bigint",
    "float4",
    "real",
    "float8",
    "double precision",
    "numeric",
    "decimal",
    "text",
    "bpchar",
    "char",
    "varchar",
    "character varying",
    "bytea",
    "date",
    "time",
    "time without time zone",
    "timestamp",
    "timestamp without time zone",
    "timestamptz",
    "timestamp with time zone",
    "uuid",
    "json",
    "jsonb",
    # Array types
    "int2[]",
    "smallint[]",
    "int4[]",
    "int[]",
    "integer[]",
    "int8[]",
    "bigint[]",
    "float4[]",
    "real[]",
    "float8[]",
    "double precision[]",
    "numeric[]",
    "decimal[]",
}
