from typing import Dict, List, Literal, Optional, Sequence, Union

import dlt
from dlt.common.schema.typing import TTableSchemaColumns
from dlt.extract import DltSource
from dlt.sources.credentials import ConnectionStringCredentials

from .helpers import (
    init_replication,
    replication_resource,
)


@dlt.source
def pg_replication(
    slot_name: str = dlt.config.value,
    pub_name: str = dlt.config.value,
    schema_name: str = dlt.config.value,
    table_names: Optional[Union[str, Sequence[str]]] = dlt.config.value,
    credentials: ConnectionStringCredentials = dlt.secrets.value,
    publish: str = "insert, update, delete",
    include_columns: Optional[Dict[str, Sequence[str]]] = None,
    columns: Optional[Dict[str, TTableSchemaColumns]] = None,
    reset: bool = False,
    include_tables: Optional[Union[str, List[str]]] = None,
    exclude_tables: Optional[Union[str, List[str]]] = None,
    initial_snapshots: bool = True,
    target_batch_size: int = 1000,
    flush_slot: bool = True,
    write_mode: Literal["merge", "append-only"] = "merge",
) -> DltSource:
    """PostgreSQL replication source with initial snapshots and ongoing replication.

    This unified source combines initial table snapshots (using connectorx for read-only access)
    with ongoing logical replication. It automatically sets up replication slots and publications.

    Args:
        slot_name (str): Name of the replication slot to be created.
        pub_name (str): Name of the publication to be created.
        schema_name (str): Postgres schema name.
        table_names (Optional[Union[str, Sequence[str]]]): Table name(s) to be
          added to the publication. If not provided, all tables in the schema
          are added.
        credentials (ConnectionStringCredentials): Postgres database credentials.
        publish (str): Comma separated list of operations to publish. Supported
          operations are `insert`, `update`, `delete`, and `truncate`. Not all
          operations are supported by all postgres versions. Only `insert` is
          supported—messages of that type are ignored.
          E.g. `publish="insert"` will create a publication that only publishes insert operations.
        include_columns (Optional[Dict[str, Sequence[str]]]): Maps table name(s) to
          sequence of names of columns to include in the snapshot table(s).
          Any column not in the sequence is excluded. If not provided, all columns
          are included.
        columns (Optional[Dict[str, TTableSchemaColumns]]): Maps
          table name(s) to column hints to apply on the snapshot table resource(s).
        reset (bool): If set to True, the existing slot and publication are dropped
          and recreated. Has no effect if a slot and publication with the provided
          names do not yet exist.
        include_tables (Optional[Union[str, List[str]]]): Glob patterns for tables to include.
          Can be used to filter tables even when not controlling publication creation.
        exclude_tables (Optional[Union[str, List[str]]]): Glob patterns for tables to exclude.
          Can be used to filter tables even when not controlling publication creation.
        initial_snapshots (bool): Whether to create read-only initial snapshot resources
          using connectorx backend.
        target_batch_size (int): Desired number of data items yielded in a batch for replication.
        flush_slot (bool): Whether processed messages are discarded from the replication slot.
        write_mode (Literal["merge", "append-only"]): Write mode for data processing.
            - "merge": Default mode. Consolidates changes with existing data, creating final tables
              that are replicas of source tables. No historical record of change events is kept.
            - "append-only": Adds data as a stream of changes (INSERT, UPDATE-INSERT, UPDATE-DELETE,
              DELETE events). Retains historical state of data with all change events preserved.

    Returns:
        DltSource: A dlt source containing both snapshot and replication resources.

    Example:
        ```python
        import dlt
        from dlt_providers.sources.pg_replication import pg_replication

        # Basic usage
        source = pg_replication(
            slot_name="my_slot",
            pub_name="my_publication",
            schema_name="public",
            credentials="postgresql://user:pass@localhost/db"
        )

        # With table filtering
        source = pg_replication(
            slot_name="my_slot",
            pub_name="my_publication",
            schema_name="public",
            include_tables=["users", "orders*"],
            exclude_tables=["*_temp"],
            credentials="postgresql://user:pass@localhost/db"
        )

        # Load the data
        pipeline = dlt.pipeline("pg_repl", destination="duckdb")
        pipeline.run(source)
        ```
    """
    # Initialize replication and get snapshot resources
    snapshot_resources = init_replication(
        slot_name=slot_name,
        pub_name=pub_name,
        schema_name=schema_name,
        table_names=table_names,
        credentials=credentials,
        publish=publish,
        include_columns=include_columns,
        columns=columns,
        reset=reset,
        include_tables=include_tables,
        exclude_tables=exclude_tables,
        initial_snapshots=initial_snapshots,
    )

    # Create replication resource
    replication_res = replication_resource(
        slot_name=slot_name,
        pub_name=pub_name,
        credentials=credentials,
        include_columns=include_columns,
        columns=columns,
        target_batch_size=target_batch_size,
        flush_slot=flush_slot,
        write_mode=write_mode,
    )

    # Combine resources
    resources = []
    if snapshot_resources:
        if isinstance(snapshot_resources, list):
            resources.extend(snapshot_resources)
        else:
            resources.append(snapshot_resources)
    resources.append(replication_res)

    return resources
