# PostgreSQL Replication

[PostgreSQL](https://www.postgresql.org/) is one of the most popular relational database management systems. This source uses PostgreSQL's logical replication functionality to efficiently process changes in tables (a process often referred to as _Change Data Capture_ or CDC). It uses [logical decoding](https://www.postgresql.org/docs/current/logicaldecoding.html) and the standard built-in `pgoutput` [output plugin](https://www.postgresql.org/docs/current/logicaldecoding-output-plugin.html).

## Features

- **Real-time Change Data Capture**: Uses PostgreSQL logical replication to capture INSERT, UPDATE, and DELETE operations
- **LSN-based Positioning**: Reliable replication positioning using PostgreSQL Log Sequence Numbers
- **Initial Snapshots**: Optional initial table snapshots using ConnectorX for high performance
- **Table Filtering**: Include/exclude tables using glob patterns
- **Column Filtering**: Include specific columns per table
- **State Management**: Automatic tracking of replication position for resumable operations
- **Batch Processing**: Configurable batch sizes for optimal performance
- **Write Modes**: Support for both merge and append-only write modes

## Requirements

### PostgreSQL Server Configuration

The PostgreSQL server must be configured for logical replication:

1. **Enable Logical Replication**:
   ```sql
   -- Add to postgresql.conf
   wal_level = logical
   max_replication_slots = 10
   max_wal_senders = 10
   ```

2. **Create Replication User**:
   ```sql
   CREATE ROLE replication_user WITH LOGIN REPLICATION;
   GRANT CREATE ON DATABASE your_database TO replication_user;
   GRANT USAGE ON SCHEMA your_schema TO replication_user;
   GRANT SELECT ON ALL TABLES IN SCHEMA your_schema TO replication_user;
   ```

3. **Create Publication and Replication Slot**:
   ```sql
   -- Create publication for tables you want to replicate
   CREATE PUBLICATION my_publication FOR ALL TABLES;
   -- or for specific tables:
   -- CREATE PUBLICATION my_publication FOR TABLE table1, table2;
   
   -- Create replication slot
   SELECT pg_create_logical_replication_slot('my_slot', 'pgoutput');
   ```

4. **Restart PostgreSQL** to apply configuration changes.

### AWS RDS Setup

1. Enable logical replication via **Parameter Group**:
   - Set `rds.logical_replication = 1`
   - Apply parameter group and restart instance

2. Grant replication privileges:
   ```sql
   GRANT rds_replication TO replication_user;
   ```

3. Use SSL connection:
   ```toml
   sources.pg_replication.credentials="postgresql://replication_user:<EMAIL>:5432/database?sslmode=require&connect_timeout=300"
   ```

### Python Dependencies

Install required packages:
```bash
pip install dlt[postgres] psycopg2-binary pypgoutput connectorx
```

## Usage

### Basic Example

```python
import dlt
from dlt_providers.sources.pg_replication import pg_replication

# Configure the source
source = pg_replication(
    slot_name="my_slot",
    pub_name="my_publication",
    schema_name="your_schema",
    credentials="postgresql://replication_user:password@localhost:5432/your_database"
)

# Create pipeline
pipeline = dlt.pipeline(
    pipeline_name="pg_replication_pipeline",
    destination="duckdb",
    dataset_name="replicated_data"
)

# Run initial load with snapshots
info = pipeline.run(source)
print(info)

# Run incremental updates
info = pipeline.run(source)
print(info)
```

### Advanced Configuration

```python
import dlt
from dlt_providers.sources.pg_replication import pg_replication

# Advanced configuration
source = pg_replication(
    slot_name="my_slot",
    pub_name="my_publication",
    schema_name="your_schema",
    credentials="postgresql://replication_user:password@localhost:5432/your_database",
    
    # Table filtering
    include_tables=["users", "orders", "products*"],  # Glob patterns
    exclude_tables=["*_temp", "*_backup"],
    
    # Column filtering
    include_columns={
        "users": ["id", "name", "email", "created_at"],
        "orders": ["id", "user_id", "total", "status"]
    },
    
    # Schema hints
    columns={
        "users": {
            "id": {"data_type": "bigint", "primary_key": True},
            "created_at": {"data_type": "timestamp"}
        }
    },
    
    # Replication settings
    target_batch_size=5000,
    write_mode="merge",  # or "append-only"
    flush_slot=True,
    
    # Performance settings
    initial_snapshots=True
)

pipeline = dlt.pipeline(
    pipeline_name="pg_replication_advanced",
    destination="snowflake",
    dataset_name="replicated_data"
)

# Run pipeline
info = pipeline.run(source)
print(info)
```

### Configuration Options

#### Core Parameters

- **slot_name** (str): Name of the replication slot to consume messages from (required)
- **pub_name** (str): Name of the publication that publishes DML operations (required)
- **schema_name** (str): PostgreSQL schema name (required)
- **credentials** (ConnectionStringCredentials): PostgreSQL connection credentials (required)

#### Table and Column Filtering

- **table_names** (Optional[Union[str, Sequence[str]]]): Specific table names to replicate
- **include_tables** (Optional[Union[str, List[str]]]): Glob patterns for tables to include
- **exclude_tables** (Optional[Union[str, List[str]]]): Glob patterns for tables to exclude
- **include_columns** (Optional[Dict[str, Sequence[str]]]): Column names to include per table
- **columns** (Optional[Dict[str, TTableSchemaColumns]]): Schema hints per table

#### Replication Settings

- **target_batch_size** (int, default=1000): Number of changes to process in each batch
- **write_mode** (Literal["merge", "append-only"], default="merge"): How to handle updates
- **flush_slot** (bool, default=True): Whether to advance the replication slot
- **initial_snapshots** (bool, default=True): Create initial table snapshots
- **reset** (bool, default=False): Reset replication state and start from beginning

## Resources

The source provides these resources:

1. **Snapshot Resources**: Initial table snapshots (when `initial_snapshots=True`)
   - Named as `{schema_name}_{table_name}`
   - Use ConnectorX for high-performance data loading
   - Automatically track completion state

2. **Replication Resource**: Ongoing change data capture
   - Named as `{slot_name}`
   - Processes logical replication messages in real-time
   - Maintains LSN position in state

## State Management

The source automatically manages replication state:

- **LSN Position**: Tracks the last processed Log Sequence Number
- **Snapshot Completion**: Tracks which table snapshots have been completed
- **Resumable**: Can resume replication from the last known position

## Data Types

Supported PostgreSQL data types:

| PostgreSQL Type | DLT Type | Notes |
|-----------------|----------|-------|
| SMALLINT, INTEGER, BIGINT | bigint | |
| BOOLEAN | bool | |
| REAL, DOUBLE PRECISION | double | |
| NUMERIC, DECIMAL | decimal | Preserves precision and scale |
| CHAR, VARCHAR, TEXT | text | |
| BYTEA | binary | |
| DATE | date | |
| TIME | time | |
| TIMESTAMP, TIMESTAMPTZ | timestamp | Converted to Pendulum datetime |
| JSON, JSONB | complex | Parsed as JSON object |
| UUID | text | |
| Arrays | complex | Converted to Python lists |
| Composite types | complex | Converted to Python dictionaries |

## Metadata Columns

The replication resource adds these metadata columns:

- **_dlt_lsn**: PostgreSQL Log Sequence Number for the change
- **_dlt_event_type**: Type of change (INSERT, UPDATE, DELETE)
- **_dlt_timestamp**: Timestamp when the change was processed
- **_dlt_deleted_ts**: Timestamp for deleted records (DELETE events only)

## Write Modes

### Merge Mode (default)
- Uses primary keys to merge changes
- Updates existing records, inserts new ones
- Handles DELETE events by setting `_dlt_deleted_ts`
- Requires primary key on target tables

### Append-Only Mode
- Appends all changes as new records
- Preserves full change history
- No deduplication
- Suitable for audit trails

## Troubleshooting

### Common Issues

1. **"replication slot does not exist"**
   - Create the replication slot using `pg_create_logical_replication_slot()`
   - Verify slot name matches configuration

2. **"publication does not exist"**
   - Create publication using `CREATE PUBLICATION`
   - Verify publication name matches configuration

3. **"permission denied for replication"**
   - Grant REPLICATION privilege to user
   - For RDS, use `GRANT rds_replication TO user`

4. **"logical replication not enabled"**
   - Set `wal_level = logical` in postgresql.conf
   - Restart PostgreSQL server

### Performance Tuning

1. **Batch Size**: Adjust `target_batch_size` based on your workload
2. **Column Filtering**: Use `include_columns` to reduce data volume
3. **Table Filtering**: Use `include_tables`/`exclude_tables` to limit scope
4. **ConnectorX**: Ensure ConnectorX is installed for fast snapshots

## Examples

### E-commerce Database Replication

```python
import dlt
from dlt_providers.sources.pg_replication import pg_replication

# Replicate e-commerce tables
source = pg_replication(
    slot_name="ecommerce_slot",
    pub_name="ecommerce_pub",
    schema_name="public",
    credentials="postgresql://repl_user:<EMAIL>:5432/ecommerce",
    include_tables=["users", "orders", "order_items", "products"],
    write_mode="merge",
    target_batch_size=2000
)

pipeline = dlt.pipeline(
    pipeline_name="ecommerce_replication",
    destination="bigquery",
    dataset_name="ecommerce_replica"
)

info = pipeline.run(source)
print(f"Loaded {info.loads_ids} loads")
```

### Audit Trail with Append-Only Mode

```python
import dlt
from dlt_providers.sources.pg_replication import pg_replication

# Create audit trail of all changes
source = pg_replication(
    slot_name="audit_slot",
    pub_name="audit_pub",
    schema_name="production",
    credentials="***************************************************/production",
    write_mode="append-only",  # Keep all change history
    initial_snapshots=False,   # Skip initial snapshots for audit
    target_batch_size=10000
)

pipeline = dlt.pipeline(
    pipeline_name="audit_trail",
    destination="postgres",
    dataset_name="audit_log"
)

info = pipeline.run(source)
print(f"Audit trail updated: {info}")
```