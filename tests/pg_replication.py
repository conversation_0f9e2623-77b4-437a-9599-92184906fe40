import dlt
import duckdb
from dlt.sources.credentials import ConnectionStringCredentials

from dlt_providers.sources.pg_replication import pg_replication

dlt.config["normalize.parquet_normalizer.add_dlt_load_id"] = True
dlt.config["normalize.parquet_normalizer.add_dlt_id"] = True

source = pg_replication(
    credentials=ConnectionStringCredentials(
        "postgresql://postgres:postgres@localhost:5432/postgres"
    ),
    slot_name="my_replication_slot",
    pub_name="my_publication",
    schema_name="inventory",
    write_mode="append-only",
)

db = duckdb.connect("test.duckdb")

pipeline = dlt.pipeline(
    pipeline_name="pg_replication",
    destination=dlt.destinations.duckdb(db),
    dataset_name="pg_replication",
)

load_info = pipeline.run(source, loader_file_format="parquet", schema_contract="evolve")
print(load_info)
print("\nAll tables:")
print(db.sql("DESCRIBE;"))
