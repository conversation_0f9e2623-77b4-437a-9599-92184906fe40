import dlt
import duckdb
from dlt.sources.credentials import ConnectionStringCredentials

from dlt_providers.sources.mysql_replication import mysql_replication

dlt.config["normalize.parquet_normalizer.add_dlt_load_id"] = True
dlt.config["normalize.parquet_normalizer.add_dlt_id"] = True

source = mysql_replication(
    credentials=ConnectionStringCredentials(
        "mysql://debezium:dbz@localhost:3306/inventory"
    ),
    server_id=123456,
    schema_name="inventory",
    write_mode="append-only",
)

db = duckdb.connect("test.duckdb")

pipeline = dlt.pipeline(
    pipeline_name="mysql_replication",
    destination=dlt.destinations.duckdb(db),
    dataset_name="mysql_replication",
)

load_info = pipeline.run(source, loader_file_format="parquet", schema_contract="evolve")
print(load_info)
print("\nAll tables:")
print(db.sql("DESCRIBE;"))
